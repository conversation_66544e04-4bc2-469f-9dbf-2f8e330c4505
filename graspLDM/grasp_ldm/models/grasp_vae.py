import warnings
from typing import Sequence, Tuple, Union

import numpy as np
import torch
from addict import Dict
from torch import Tensor, nn

from grasp_ldm.losses.builder import build_loss_from_cfg
from grasp_ldm.utils.config import Config

from .modules.base_network import BaseGraspSampler
from .modules.pc_encoders import PVCNN2Encoder, PVCNNEncoder
from .modules.resnets import ResNet1D, Unet1D


class GraspCVAE(BaseGraspSampler):
    """
    Conditional Variational Autoencoder for Grasp Generation
    
    =============================================================================
    ARCHITECTURE OVERVIEW - ENCODERS AND DECODERS RELATIONSHIPS
    =============================================================================
    
    This model contains multiple encoders and decoders working together:
    
    1. MAIN ENCODER: PcConditionedGraspEncoder
       ├── PC Encoder (PVCNNEncoder/PVCNN2Encoder)
       │   ├── Input: Point cloud [B, N, 3]
       │   └── Output: z_pc [B, pc_latent_size] (conditioning information)
       │
       └── Grasp Encoder (ConditionalGraspPoseEncoder)
           ├── Input: Grasp pose [B, 1, 6] + z_pc [B, pc_latent_size]
           └── Output: z_grasp [B, 1, grasp_latent_size]
    
    2. VAE BOTTLENECK: VAEBottleneck
       ├── Input: z_grasp [B, grasp_latent_size]
       └── Output: (μ, logvar) → reparameterized z_grasp [B, grasp_latent_size]
    
    3. MAIN DECODER: ConditionalGraspPoseDecoder
       ├── Input: z_grasp [B, grasp_latent_size] + z_pc [B, pc_latent_size]
       └── Output: Reconstructed grasp [B, 6] + classification [B, 1] + qualities [B, num_qualities]
    
    =============================================================================
    DATA FLOW DURING TRAINING:
    =============================================================================
    
    Point Cloud [B,N,3] ──→ PC Encoder ──→ z_pc [B,D_zpc] ──┐
                                                             │
                                                             ▼
    Grasp Pose [B,6] ──→ Grasp Encoder ──→ z_grasp [B,1,D_zg]
                              ▲                              │
                              │                              ▼
                              └──── (conditioned on z_pc)   │
                                                             ▼
                                                      VAEBottleneck
                                                             │
                                                             ▼
                                                    (μ,logvar) → z_grasp [B,D_zg]
                                                             │
    z_pc [B,D_zpc] ──────────────────────────────────────────┼─────────┐
                                                             │         │
                                                             ▼         ▼
                                                    ConditionalGraspPoseDecoder
                                                             │
                                                             ▼
                                              ┌─ Reconstructed Grasp [B,6]
                                              ├─ Classification [B,1]  
                                              └─ Qualities [B,num_qualities]
    
    =============================================================================
    DATA FLOW DURING INFERENCE (Generation):
    =============================================================================
    
    Point Cloud [B,N,3] ──→ PC Encoder ──→ z_pc [B,D_zpc]
                                               │
    Sample z_grasp ~ N(0,I) [B*num_grasps,D_zg] ──→ ConditionalGraspPoseDecoder
                                               │                    │
                                               └────────────────────┘
                                                                    ▼
                                              ┌─ Generated Grasps [B*num_grasps,6]
                                              ├─ Classification [B*num_grasps,1]
                                              └─ Qualities [B*num_grasps,num_qualities]
    
    =============================================================================
    KEY RELATIONSHIPS:
    =============================================================================
    
    • PC Encoder: Extracts geometric features from point clouds
    • Grasp Encoder: Encodes grasp poses CONDITIONED on point cloud features
    • VAE Bottleneck: Creates probabilistic latent space for grasp generation
    • Grasp Decoder: Reconstructs grasps CONDITIONED on point cloud features
    • Point cloud features (z_pc) serve as CONDITIONING for both encoding and decoding
    
    =============================================================================
    
    Pipeline Overview:
    1. Input: Point cloud [B, N, 3] + Grasp pose [B, 6] (during training)
    2. Encoding: Point cloud → z_pc [B, pc_latent_size], Grasp → z_grasp [B, grasp_latent_size]
    3. VAE Bottleneck: z_grasp → (mu, logvar) → reparameterized z_grasp
    4. Decoding: (z_grasp, z_pc) → reconstructed grasp [B, 6] + optional (classification [B, 1], qualities [B, num_qualities])
    5. Loss computation: reconstruction + KL divergence + optional classification/quality losses
    
    During inference:
    - Input: Point cloud [B, N, 3]
    - Sample: z_grasp ~ N(0, I) [B, grasp_latent_size]
    - Output: Generated grasps [B, num_grasps, 6]
    """
    def __init__(
        self,
        grasp_latent_size: int,
        pc_latent_size: int,
        grasp_encoder_config: dict,
        pc_encoder_config: dict,
        decoder_config: dict,
        loss_config: dict,
        intermediate_feature_resolution: int = 16,
        num_output_qualities: Union[int, None] = None,
    ) -> None:
        """Grasp VAE

        Clarify features channels and batch
        Args:
            grasp_latent_size (int): Dimension of grasp latent space
            pc_latent_size (int): Dimension of point cloud latent space
            grasp_encoder_config (dict): Configuration for grasp encoder
            pc_encoder_config (dict): Configuration for point cloud encoder
            decoder_config (dict): Configuration for decoder
            loss_config (dict): Configuration for loss functions
            intermediate_feature_resolution (int, optional): Feature resolution for intermediate layers. Defaults to 16.
            num_output_qualities (Union[int, None], optional): Number of quality outputs. Defaults to None.

        """
        ## Using super in the following might fail if the base classes have arguments
        super().__init__()

        # Encoders latent feature dims
        self.grasp_latent_size = grasp_latent_size  # Dimension of grasp latent space
        self.pc_latent_size = pc_latent_size        # Dimension of point cloud latent space

        ## Losses
        self.loss_config = loss_config

        assert (
            "reconstruction_loss" in loss_config and "latent_loss" in loss_config
        ), "`reconstruction_loss` and `latent_loss` must be specified in loss_config"

        # TODO: Build only if in train mode
        self.reconstruction_loss = build_loss_from_cfg(loss_config.reconstruction_loss)
        self.latent_loss = build_loss_from_cfg(loss_config.latent_loss)

        # Optional losses
        self.classification_loss = (
            build_loss_from_cfg(loss_config.classification_loss)
            if hasattr(loss_config, "classification_loss")
            else None
        )

        self.quality_loss = (
            build_loss_from_cfg(loss_config.quality_loss)
            if hasattr(loss_config, "quality_loss")
            else None
        )

        ## Sub-networks
        # Encoder: (xyz [B,N,3], grasp [B,6]) → (z_grasp [B,grasp_latent_size], z_pc [B,pc_latent_size])
        self.encoder = PcConditionedGraspEncoder(
            pc_encoder_config=pc_encoder_config,
            grasp_encoder_config=grasp_encoder_config,
            pc_latent_size=self.pc_latent_size,
            grasp_latent_size=self.grasp_latent_size,
        )

        # VAE Bottleneck: z_grasp [B,grasp_latent_size] → (mu, logvar) → reparameterized z_grasp
        self.bottleneck = VAEBottleneck(
            in_features=self.encoder.out_features,
            latent_size=self.grasp_latent_size,
        )

        # Learn qualities?
        self.num_output_qualities = num_output_qualities
        # Decoder: (z_grasp [B,grasp_latent_size], z_pc [B,pc_latent_size]) → outputs
        self.decoder = ConditionalGraspPoseDecoder(
            in_features=grasp_latent_size,
            config=decoder_config,
            num_output_qualities=self.num_output_qualities,
            feature_resolution=intermediate_feature_resolution,
        )

        self.out_features = self.decoder.out_features

    @property
    def latent_losses(self):
        """Get latent losses for annealing etc"""
        return [self.latent_loss]

    @property
    def use_grasp_qualities(self) -> bool:
        return True if self.decoder._use_qualities else False

    def encode(self, xyz: Tensor, grasp: Tensor) -> Sequence:
        """Encode inputs into latent representations

        Args:
            xyz (Tensor): Point cloud coordinates [B, N, 3]
            grasp (Tensor): Grasp pose parameters [B, 6] where 6 = [translation(3) + rotation_mrp(3)]

        Returns:
            Sequence: 
                - Grasp latents: (mu [B, grasp_latent_size], logvar [B, grasp_latent_size], z_grasp [B, grasp_latent_size])
                - PC latents: (None, None, z_pc [B, pc_latent_size])
        """
        # Encode both point cloud and grasp
        # z_grasp: [B, 1, grasp_latent_size], z_pc: [B, pc_latent_size]
        z_grasp, z_pc = self.encoder(xyz, grasp)

        # VAE bottleneck: z_grasp [B, grasp_latent_size] → (mu, logvar) → reparameterized z_grasp
        mu, logvar = self.bottleneck(z_grasp.squeeze(dim=-2))  # Remove middle dimension: [B, 1, D] → [B, D]
        z_grasp = self.bottleneck.reparameterize(mu, logvar)   # Sample from N(mu, exp(0.5*logvar))
        return (mu, logvar, z_grasp), (None, None, z_pc)

    def forward(
        self, xyz: Tensor, grasp: Tensor, compute_loss: bool = True, **kwargs
    ) -> Union[Tensor, Tuple[Tensor, Dict]]:
        """Forward pass through the entire pipeline

        Args:
            xyz (Tensor): Point cloud coordinates [B, N, 3]
            grasp (Tensor): Ground truth grasp pose [B, 6] where 6 = [translation(3) + rotation_mrp(3)]
            compute_loss (bool, optional): Whether to compute training losses. Defaults to True.

        Returns:
            Union[Tensor, Tuple[Tensor, Dict]]: 
                - If compute_loss=False: List of output tensors
                - If compute_loss=True: (output_list, loss_dict)
                
        Output dimensions:
            - tmrp: [B, 6] - reconstructed grasp pose
            - class_logits: [B, 1] - grasp classification logits  
            - qualities: [B, num_qualities] - grasp quality scores (optional)
        """
        # Encode to latent distribution
        # mu_h, logvar_h: [B, grasp_latent_size], z_h: [B, grasp_latent_size], z_pc: [B, pc_latent_size]
        (mu_h, logvar_h, z_h), (_, _, z_pc) = self.encode(xyz, grasp)

        # Decode latent codes to output space
        # out: List containing [tmrp(B,6), class_logits(B,1), qualities(B,num_qualities)]
        out = self.decoder(z_h, z_pc)

        if compute_loss:
            # Concatenate all outputs for loss computation
            loss_dict = self.loss_fn(
                x_in=grasp,
                x_out=torch.concat(out, -1),  # Concatenate along last dimension
                grasp_mu_logvar=(mu_h, logvar_h),
                **kwargs,
            )
            return out, loss_dict
        else:
            return out

    def loss_fn(
        self,
        x_in: Tensor,
        x_out: Tensor,
        grasp_mu_logvar: Tuple[Tensor, Tensor],
        **kwargs,
    ) -> Dict:
        """Loss function for training
        
        Computes multiple loss components:
        1. Reconstruction loss: MSE between input and output grasp poses
        2. KL divergence loss: Regularizes latent space to be close to N(0,I)
        3. Classification loss: Cross-entropy for grasp success classification (optional)
        4. Quality loss: MSE for grasp quality prediction (optional)
        
        Args:
            x_in (Tensor): [B, D_g] ground truth grasp pose parameters
            x_out (Tensor): [B, D_g] predicted grasp pose parameters (concatenated outputs)
            grasp_mu_logvar (tuple): encoded mu logvar from grasp encoder
                                    (mu:[B, D_zg], logvar:[B, D_zg])

        Returns:
            dict: loss dictionary with individual loss components and total loss
        """
        loss_dict = Dict(loss=0)

        # Extract grasp pose components (first 6 dimensions: translation + rotation)
        grasps_in = x_in[..., :6]    # [B, 6] - ground truth grasp pose
        grasps_out = x_out[..., :6]  # [B, 6] - predicted grasp pose

        # Reconstruction loss: L2 distance between input and output grasp poses
        loss_dict.reconstruction_loss = self.reconstruction_loss(
            grasps_in.squeeze(), grasps_out.squeeze(), **kwargs
        )

        # Latent loss: KL divergence D_KL(q(z|x) || p(z)) where p(z) = N(0,I)
        (
            loss_dict.latent_loss,
            loss_dict._unweighted_kld,
        ) = self.latent_loss(*grasp_mu_logvar, return_unweighted=True, **kwargs)

        # Classification loss (optional): Cross-entropy for grasp success prediction
        if self.classification_loss is not None:
            cls_in = x_in[..., 6]    # [B] - ground truth classification labels
            cls_out = x_out[..., 6]  # [B] - predicted classification logits

            loss_dict.classification_loss = self.classification_loss(
                output=cls_out.squeeze(), targets=cls_in.squeeze(), **kwargs
            )

        # Quality loss (optional): MSE for grasp quality scores
        if self.quality_loss is not None:
            quals_in = x_in[..., 7:]   # [B, num_qualities] - ground truth quality scores
            quals_out = x_out[..., 7:] # [B, num_qualities] - predicted quality scores

            loss_dict.quality_loss = self.quality_loss(
                quals_in.squeeze(), quals_out.squeeze(), **kwargs
            )

        # Total loss: sum of all loss components
        # Note: _unweighted_kld is not added to total loss (only for monitoring)
        loss_dict.loss += (
            loss_dict.latent_loss
            + loss_dict.reconstruction_loss
            + (loss_dict.quality_loss if self.quality_loss is not None else 0)
            + (
                loss_dict.classification_loss
                if self.classification_loss is not None
                else 0
            )
        )

        return loss_dict

    def encode_pc(self, xyz: Tensor) -> Tensor:
        """Helper function to encode pointclouds
        
        Args:
            xyz (Tensor): Point cloud coordinates [B, N, 3]
            
        Returns:
            Tensor: Point cloud latent representation [B, pc_latent_size]
        """
        return self.encoder.encode_pc(xyz)

    def sample_grasp_latent(self, batch_size: int, device: int) -> Tensor:
        """Helper function to sample grasp latent codes from standard normal distribution
        
        Args:
            batch_size (int): Number of samples to generate
            device (int): Device to place tensors on
            
        Returns:
            Tensor: Sampled grasp latent codes [batch_size, grasp_latent_size]
        """
        return torch.randn(batch_size, self.grasp_latent_size).to(device)

    def generate_grasps(self, xyz: Tensor, num_grasps: int = 10) -> Tensor:
        """Generates grasps for a given pointcloud (inference mode)
        
        Pipeline for generation:
        1. Encode point cloud: xyz [B, N, 3] → z_pc [B, pc_latent_size]
        2. Sample grasp latents: z_grasp ~ N(0, I) [B*num_grasps, grasp_latent_size]
        3. Repeat point cloud conditioning for each grasp
        4. Decode: (z_grasp, z_pc) → generated grasps

        Args:
            xyz (Tensor): [B, N, 3] pointcloud coordinates
            num_grasps (int, optional): Number of grasps to generate per point cloud. Defaults to 10.

        Returns:
            Tensor: Generated grasp poses and optional outputs
                - If no qualities: List[tmrp [B*num_grasps, 6], class_logits [B*num_grasps, 1]]
                - If with qualities: List[tmrp [B*num_grasps, 6], class_logits [B*num_grasps, 1], qualities [B*num_grasps, num_qualities]]
        """
        assert (
            xyz.ndim == 3
        ), f"Input pointcloud should be  3-dim tensor of shape [B, N, 3]. Found a {xyz.ndim} dimensional tensor."

        # Number of input point clouds
        num_pcs = xyz.shape[0]  # B

        # Encode input pointcloud: [B, N, 3] → [B, pc_latent_size]
        z_pc_cond = self.encode_pc(xyz)

        # Repeat pointcloud conditioning latent code for each grasp
        # [B, pc_latent_size] → [B*num_grasps, pc_latent_size]
        z_pc_cond = z_pc_cond.repeat_interleave(num_grasps, dim=0)

        # Sample grasp latent codes from standard normal distribution
        # [B*num_grasps, grasp_latent_size]
        z_h = torch.randn(num_pcs * num_grasps, self.grasp_latent_size).to(xyz.device)

        # Decode grasp latent code conditioned on point cloud
        # Input: z_h [B*num_grasps, grasp_latent_size], z_pc_cond [B*num_grasps, pc_latent_size]
        # Output: List of tensors with generated grasps and optional classifications/qualities
        out = self.decoder(z_h, z_pc_cond)

        return out


class PcConditionedGraspEncoder(nn.Module):
    """
    Point Cloud Conditioned Grasp Encoder
    
    Encodes grasp input into a point cloud-conditional latent code.
    
    Pipeline:
    1. Point cloud encoding: xyz [B, N, 3] → z_pc [B, pc_latent_size]
    2. Grasp encoding: grasp [B, 6] → z_grasp [B, 1, grasp_latent_size] (conditioned on z_pc)
    3. Output: (z_grasp, z_pc)
    """

    PC_ENCODERS = {
        "PVCNNEncoder": PVCNNEncoder,
        "PVCNN2Encoder": PVCNN2Encoder,
    }  # "PointNet2Encoder": PointNet2Encoder, }

    def __init__(
        self,
        pc_encoder_config: Config,
        grasp_encoder_config: Config,
        pc_latent_size: int = 64,
        grasp_latent_size: int = 4,
    ) -> None:
        """Initialize Point Cloud Conditioned Grasp Encoder

        Args:
            pc_encoder_config (Config): Point cloud encoder configuration
            grasp_encoder_config (Config): Grasp encoder configuration  
            pc_latent_size (int, optional): Point cloud latent dimension. Defaults to 64.
            grasp_latent_size (int, optional): Grasp latent dimension. Defaults to 4.
        """
        super().__init__()

        if pc_encoder_config.type not in self.PC_ENCODERS:
            raise NotImplementedError(
                f"Pointcloud encoder network arch of type=`{pc_encoder_config.type}` is not implemented. \
				Available base network types are: {list(self.PC_ENCODERS)}"
            )

        # Point cloud encoder: xyz [B, N, 3] → z_pc [B, pc_latent_size]
        self.pc_encoder = self.PC_ENCODERS[pc_encoder_config.type](
            out_features=pc_latent_size, **pc_encoder_config.args
        )

        # Grasp encoder: (grasp [B, 1, 6], z_pc [B, pc_latent_size]) → z_grasp [B, 1, grasp_latent_size]
        self.grasp_encoder = ConditionalGraspPoseEncoder(
            config=grasp_encoder_config, latent_size=grasp_latent_size
        )

        self.out_features = grasp_latent_size

    def forward(
        self,
        xyz: Tensor,
        h: Tensor,
        z_pc: Tensor = None,
    ) -> Tensor:
        """Forward pass through point cloud conditioned grasp encoder
        TODO: Shape spec

        In case z_pc is already available at this method's call,
        the forward method will avoid double computation, by bypassing
        the encode_pc step

        If using simple encoder (FC) to encode grasp, there is no proper conditioning on z_pc.
        so, use a bunch of simple FC fusion layers on concatenated tensor
        NOTE: This is just to support older experimental VAE models

        When not using simple encoder, fuse
        Dimension flow:
        1. xyz [B_pc, N, 3] → z_pc [B_pc, pc_latent_size]
        2. h [B_h, 6] → h [B_h, 1, 6] (unsqueeze)
        3. z_pc [B_pc, pc_latent_size] → z_pc [B_h, pc_latent_size] (repeat if B_h > B_pc)
        4. (h [B_h, 1, 6], z_pc [B_h, pc_latent_size]) → z_grasp [B_h, 1, grasp_latent_size]

        Args:
            xyz (Tensor): Point cloud coordinates [B_pc, N, 3]
            h (Tensor): Grasp poses [B_h, 6] where 6 = [translation(3) + rotation_mrp(3)]
            z_pc (Tensor, optional): Pre-computed point cloud latents [B_pc, pc_latent_size]. 
                                   If None, will be computed from xyz. Defaults to None.

        Returns:
            Tuple[Tensor, Tensor]: 
                - z_grasp: Grasp latent codes [B_h, 1, grasp_latent_size]
                - z_pc: Point cloud latent codes [B_h, pc_latent_size]
        """
        batch_size_h = h.shape[0]    # Number of grasp samples
        batch_size_pc = xyz.shape[0] # Number of point clouds

        # Calculate how many times to repeat each point cloud latent
        pc_repeats = batch_size_h // batch_size_pc

        # Add sequence dimension to grasp: [B_h, 6] → [B_h, 1, 6]
        h = h.unsqueeze(dim=1)
        
        # Encode point cloud if not provided
        if z_pc is None:
            assert xyz is not None
            # Encode point cloud and repeat for each grasp
            # [B_pc, N, 3] → [B_pc, pc_latent_size] → [B_h, pc_latent_size]
            z_pc = self.pc_encoder(xyz).repeat_interleave(pc_repeats, dim=0)

        # Encode grasp conditioned on point cloud
        # Input: h [B_h, 1, 6], z_pc [B_h, pc_latent_size]
        # Output: z_grasp [B_h, 1, grasp_latent_size]
        z_grasp = self.grasp_encoder(h, cond=z_pc)

        return z_grasp, z_pc

    def encode_pc(self, xyz: Tensor) -> Tensor:
        """Encode point cloud to latent representation
        
        Args:
            xyz (Tensor): Point cloud coordinates [B, N, 3]
            
        Returns:
            Tensor: Point cloud latent codes [B, pc_latent_size]
        """
        return self.pc_encoder(xyz)

    def get_conditioning_latent(self, xyz: Tensor) -> Tensor:
        """Get conditioning latent from point cloud (alias for encode_pc)
        
        Args:
            xyz (Tensor): Point cloud coordinates [B, N, 3]
            
        Returns:
            Tensor: Point cloud latent codes [B, pc_latent_size]
        """
        return self.encode_pc(xyz)


class ConditionalGraspPoseDecoder(nn.Module):
    """
    Conditional Grasp Pose Decoder
    
    Decodes latent codes to grasp pose outputs in the original space.
    
    Pipeline:
    1. Input layer: z_h [B, grasp_latent_size] → [B, feature_resolution]
    2. Unsqueeze: [B, feature_resolution] → [B, 1, feature_resolution]
    3. Core network: [B, 1, feature_resolution] → [B, 1, net_out_features] (conditioned on z_pc)
    4. Squeeze: [B, 1, net_out_features] → [B, net_out_features]
    5. Output heads:
       - tmrp: [B, net_out_features] → [B, 6] (translation + rotation)
       - class_logits: [B, net_out_features] → [B, 1] (grasp classification)
       - qualities: [B, net_out_features] → [B, num_qualities] (optional quality scores)
    """

    MODELS = {"Unet1D": Unet1D, "ResNet1D": ResNet1D}

    def __init__(
        self, config, in_features, feature_resolution, num_output_qualities=None
    ) -> None:
        """Initialize Conditional Grasp Pose Decoder
        
        Args:
            config: Decoder network configuration
            in_features (int): Input feature dimension (grasp_latent_size)
            feature_resolution (int): Intermediate feature resolution
            num_output_qualities (int, optional): Number of quality outputs. Defaults to None.
        """
        super().__init__()

        if config.type not in self.MODELS:
            raise NotImplementedError(
                f"Base network arch of type=`{config.type}` is not implemented. \
				Available base network types are: {list(self.MODELS)}"
            )

        # Base Decoder network
        self.in_features = in_features              # grasp_latent_size
        self.feature_resolution = feature_resolution # Intermediate feature dimension

        # Input layer: [B, grasp_latent_size] → [B, feature_resolution]
        self.in_layer = nn.Linear(
            in_features=self.in_features, out_features=self.feature_resolution
        )

        # Core network: [B, 1, feature_resolution] → [B, 1, net_out_features]
        # Operates on sequences with point cloud conditioning
        self.net = self.MODELS[config.type](dim=feature_resolution, **config.args)
        _net_out_features = self.net.out_features

        # Output layers
        self.tmrp = nn.Linear(_net_out_features, 6)  # Translation + Modified Rodriguez Parameters
        self.class_logits = nn.Linear(_net_out_features, 1)  # Grasp success classification

        self._use_qualities = (
            True
            if num_output_qualities is not None and num_output_qualities > 0
            else False
        )

        # Optional quality prediction head
        if self._use_qualities:
            self.num_qualities = num_output_qualities
            self.qualities = nn.Linear(_net_out_features, num_output_qualities)
            self.out_features = (6, 1, num_output_qualities)  # (tmrp, class, qualities)
        else:
            self.num_qualities = None
            self.out_features = (6, 1)  # (tmrp, class)

    def forward(
        self, z_h: Tensor, cond: Tensor = None
    ) -> Tuple[Tensor, Tensor, Tensor]:
        """Forward pass through decoder
        
        Dimension flow:
        1. z_h [B, grasp_latent_size] → [B, feature_resolution]
        2. [B, feature_resolution] → [B, 1, feature_resolution] (unsqueeze)
        3. [B, 1, feature_resolution] → [B, 1, net_out_features] (core network with conditioning)
        4. [B, 1, net_out_features] → [B, net_out_features] (squeeze)
        5. [B, net_out_features] → multiple output heads

        Args:
            z_h (Tensor): Grasp latent codes [B, grasp_latent_size]
            cond (Tensor, optional): Point cloud conditioning latents [B, pc_latent_size].
                                   Defaults to None.

        Returns:
            Tuple containing:
                - tmrp: [B, 6] - Translation (3) + Modified Rodriguez Parameters (3)
                - cls_logits: [B, 1] - Grasp classification logits
                - qualities: [B, num_qualities] - Quality scores (optional)
        """
        # Input layer: [B, grasp_latent_size] → [B, feature_resolution]
        z_h = self.in_layer(z_h)

        # Unsqueeze to sequence format: [B, feature_resolution] → [B, 1, feature_resolution]
        z_h = z_h.unsqueeze(-2)

        # Core network with point cloud conditioning
        # Input: z_h [B, 1, feature_resolution], cond [B, pc_latent_size]
        # Output: [B, 1, net_out_features]
        z_h = self.net(z_h, z_cond=cond)

        # Squeeze sequence dimension: [B, 1, net_out_features] → [B, net_out_features]
        z_h = z_h.squeeze(-2)

        # Decode to output space
        tmrp = self.tmrp(z_h)           # [B, 6] - grasp pose (translation + rotation)
        cls_logits = self.class_logits(z_h)  # [B, 1] - classification logits

        res = (tmrp, cls_logits)

        # Optional quality prediction
        if self._use_qualities:
            quals = self.qualities(z_h)  # [B, num_qualities] - quality scores
            res += (quals,)

        return res


class ConditionalGraspPoseEncoder(nn.Module):
    """
    Conditional Grasp Pose Encoder
    
    Encodes grasp poses conditioned on point cloud features.
    
    Pipeline:
    1. Input layer: grasp [B, 1, 6] → [B, 1, feature_resolution]
    2. Core network: [B, 1, feature_resolution] → [B, 1, net_out_features] (conditioned on z_pc)
    3. Output layer: [B, 1, net_out_features] → [B, 1, latent_size]
    """
    
    MODELS = {"Unet1D": Unet1D, "ResNet1D": ResNet1D}

    def __init__(
        self,
        config: Config,
        latent_size: int,
        feature_resolution: int = 16,
    ) -> None:
        """Conditional Grasp Pose Encoder

        Args:
            config (Config): Encoder model config for the required model type
            latent_size (int): Size of the grasp latent output
            feature_resolution (int, optional): Resolution of intermediate features. Defaults to 16.
        """
        super().__init__()

        # Input feature dimension (typically 6 for grasp pose)
        self.in_features = config.args.in_features
        self.out_features = latent_size

        assert config.type in list(
            self.MODELS
        ), f"Cannot build GraspPoseEncoder of model_type: {config.type} from supported models: {self.MODELS}"

        # Resolve feature resolution for intermediate layers
        self.feature_resolution = None
        self._resolve_feature_resolution(feature_resolution)

        ## Network layers

        # Input layer: [B, 1, in_features] → [B, 1, feature_resolution]
        self.in_layer = nn.Linear(
            in_features=self.in_features, out_features=self.feature_resolution
        )

        # Core network: [B, 1, feature_resolution] → [B, 1, net_out_features]
        # Operates on sequences with point cloud conditioning
        _ = config.args.pop("in_features") if "in_features" in config.args else None
        self.net = self.MODELS[config.type](dim=feature_resolution, **config.args)

        # Output layer: [B, 1, net_out_features] → [B, 1, latent_size]
        self.out_layer = nn.Linear(
            in_features=self.net.out_features, out_features=self.out_features
        )

    def _resolve_feature_resolution(self, feature_resolution: int) -> None:
        """Resolves the feature resolution to be used in the network

        Args:
            feature_resolution (int): Resolution of the features to be maintained
                        in the network. Defaults to 16.

        Raises:
            ValueError: If feature_resolution is < 0

        Returns:
            None: Sets the feature_resolution attribute
        """

        assert feature_resolution is not None, "feature_resolution cannot be None"
        if feature_resolution < 0:
            raise ValueError(
                f"feature_resolution must be >= 0, got {feature_resolution}"
            )
        elif feature_resolution > 0 and feature_resolution < self.in_features:
            warnings.warn(
                f"feature_resolution ({feature_resolution}) is less than in_features ({self.in_features})"
                "This is not a good idea."
            )
            self.feature_resolution = feature_resolution
        else:
            self.feature_resolution = feature_resolution

        return

    def forward(self, x: torch.Tensor, cond: torch.Tensor) -> torch.Tensor:
        """Forward pass through conditional grasp encoder
        
        Dimension flow:
        1. x [B, 1, in_features] → [B, 1, feature_resolution] (input layer)
        2. [B, 1, feature_resolution] → [B, 1, net_out_features] (core network with conditioning)
        3. [B, 1, net_out_features] → [B, 1, latent_size] (output layer)

        Args:
            x (torch.Tensor): Input grasp tensor [B, 1, in_features] where in_features=6
            cond (torch.Tensor): Point cloud conditioning tensor [B, pc_latent_size]

        Returns:
            torch.Tensor: Encoded grasp latent [B, 1, latent_size]
        """
        # Input layer: [B, 1, in_features] → [B, 1, feature_resolution]
        x = self.in_layer(x)
        
        # Core network with point cloud conditioning
        # Input: x [B, 1, feature_resolution], cond [B, pc_latent_size]
        # Output: [B, 1, net_out_features]
        x = self.net(x, z_cond=cond)
        
        # Output layer: [B, 1, net_out_features] → [B, 1, latent_size]
        x = self.out_layer(x)

        return x


class VAEBottleneck(nn.Module):
    """
    VAE Bottleneck Layer
    
    Implements the reparameterization trick for variational autoencoders.
    
    Pipeline:
    1. Input: z [B, latent_size]
    2. Linear layers: z → (mu [B, latent_size], logvar [B, latent_size])
    3. Reparameterization: (mu, logvar) → z_sampled [B, latent_size] where z_sampled ~ N(mu, exp(0.5*logvar))
    """
    
    def __init__(self, in_features: int, latent_size: int) -> None:
        """VAE Bottleneck

        Args:
            in_features (int): Input feature size (typically grasp_latent_size)
            latent_size (int): Latent size (same as in_features for this implementation)
        """
        super().__init__()

        # Linear layers to predict mean and log variance
        self.mu = nn.Linear(in_features, latent_size)      # Mean: [B, in_features] → [B, latent_size]
        self.logvar = nn.Linear(in_features, latent_size)  # Log variance: [B, in_features] → [B, latent_size]

    def reparameterize(self, mu: torch.Tensor, logvar: torch.Tensor) -> torch.Tensor:
        """Sample from N(mu, var) using the reparameterization trick
        
        Implements: z = mu + eps * std where eps ~ N(0, I) and std = exp(0.5 * logvar)

        Args:
            mu (torch.Tensor): Mean [B, latent_size]
            logvar (torch.Tensor): Log variance [B, latent_size]
            
        Returns:
            torch.Tensor: Sampled latent codes [B, latent_size]
        """
        std = torch.exp(0.5 * logvar)  # Convert log variance to standard deviation
        eps = torch.randn_like(std)    # Sample noise from standard normal
        return mu + eps * std          # Reparameterization trick

    def forward(self, z: Tensor) -> Tuple[Tensor, Tensor]:
        """Forward pass through VAE bottleneck

        Args:
            z (torch.Tensor): Input tensor [B, in_features]

        Returns:
            Tuple[Tensor, Tensor]: (mu [B, latent_size], logvar [B, latent_size])
        """
        mu = self.mu(z)        # Predict mean
        logvar = self.logvar(z) # Predict log variance
        return mu, logvar
