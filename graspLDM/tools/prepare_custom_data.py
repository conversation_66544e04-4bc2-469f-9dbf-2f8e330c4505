import numpy as np
import os
import argparse

def prepare_data(pc_path: str, output_dir: str = 'my_custom_dataset'):
    """
    Prepares a custom point cloud to be used with the original generate_grasps.py script.

    This function creates a mini-dataset with a directory structure that mimics
    the ACRONYM dataset, allowing the script to load a custom point cloud without
    any code modifications.

    Args:
        pc_path (str): Path to the input point cloud .npy file (shape [N, 3]).
        output_dir (str, optional): The directory to create the mini-dataset in.
                                    Defaults to 'my_custom_dataset'.
    """
    print(f"Loading custom point cloud from: {pc_path}")
    # 1. Load the point cloud
    try:
        pc = np.load(pc_path)
    except Exception as e:
        print(f"Error loading point cloud file: {e}")
        return

    if not isinstance(pc, np.ndarray) or pc.ndim != 2 or pc.shape[1] != 3:
        raise ValueError(f"Point cloud at {pc_path} must be a numpy array with shape (N, 3).")

    print(f"Point cloud loaded with {pc.shape[0]} points.")

    # 2. Create the necessary directory structure
    scene_dir = os.path.join(output_dir, 'test', 'scene_000000')
    os.makedirs(scene_dir, exist_ok=True)
    print(f"Created directory structure at: {scene_dir}")

    # 3. Create placeholder data required by the AcronymPartialPointclouds dataset class
    # The script expects these fields in the .npz file, even if they are not used for inference.
    # We provide empty or dummy values to prevent loading errors.
    placeholder_grasps = np.array([])
    placeholder_success = np.array([])
    placeholder_qualities = {
        'epsilon': np.array([]),
        'volume': np.array([]),
        'depth': np.array([]),
        'wrench_torque': np.array([]),
        'wrench_force': np.array([]),
        'collision_count': np.array([])
    }

    # The dataset class expects the grasps to be inside a dictionary
    grasp_dict = {
        'transforms': placeholder_grasps,
        'success': placeholder_success,
        'qualities': placeholder_qualities
    }

    # 4. Define the output path for the .npz file
    output_npz_path = os.path.join(scene_dir, 'grasps_000.npz')

    # 5. Save the data in the required format.
    # The key is to save your point cloud under the 'pc' key.
    # We use np.savez to save multiple arrays into a single file.
    # The allow_pickle=True is important for the dictionary.
    np.savez(
        output_npz_path,
        pc=pc.astype(np.float32),
        grasps=grasp_dict,
        obj_path='custom_object', # Placeholder
        renders={} # Placeholder
    )

    print(f"Successfully created '{output_npz_path}' for inference.")
    print("\n--- Next Steps ---")
    print("You can now run the grasp generation script.")
    print("Use the following command, replacing the --exp_path with your model's path:")
    print(f"\npython tools/generate_grasps.py \\")
    print(f"    --exp_path /path/to/your/experiment \\")
    print(f"    --mode LDM \\")
    print(f"    --data_root {output_dir} \\")
    print(f"    --split test \\")
    print(f"    --num_samples 1 \\")
    print(f"    --num_grasps 20 \\")
    print(f"    --visualize\n")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Prepare a custom point cloud for GraspLDM inference.")
    parser.add_argument(
        '--pc_path',
        type=str,
        required=True,
        help='Path to your custom point cloud .npy file (shape [N, 3]).'
    )
    parser.add_argument(
        '--output_dir',
        type=str,
        default='my_custom_dataset',
        help="The directory to create the mini-dataset in. Defaults to 'my_custom_dataset'."
    )
    args = parser.parse_args()

    prepare_data(args.pc_path, args.output_dir) 